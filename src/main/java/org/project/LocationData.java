package org.project;

/**
 * Data model class to represent location information retrieved from IP geolocation API
 */
public class LocationData {
    private String ip;
    private String city;
    private String region;
    private String country;
    private String countryCode;
    private String timezone;
    private String latitude;
    private String longitude;
    private String isp;
    private String organization;
    private String postal;

    // Default constructor
    public LocationData() {}

    // Constructor with essential fields
    public LocationData(String ip, String city, String region, String country) {
        this.ip = ip;
        this.city = city;
        this.region = region;
        this.country = country;
    }

    // Getters and Setters
    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getTimezone() {
        return timezone;
    }

    public void setTimezone(String timezone) {
        this.timezone = timezone;
    }

    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }

    public String getIsp() {
        return isp;
    }

    public void setIsp(String isp) {
        this.isp = isp;
    }

    public String getOrganization() {
        return organization;
    }

    public void setOrganization(String organization) {
        this.organization = organization;
    }

    public String getPostal() {
        return postal;
    }

    public void setPostal(String postal) {
        this.postal = postal;
    }

    @Override
    public String toString() {
        return String.format(
            "LocationData{ip='%s', city='%s', region='%s', country='%s', countryCode='%s', " +
            "timezone='%s', latitude='%s', longitude='%s', isp='%s', organization='%s', postal='%s'}",
            ip, city, region, country, countryCode, timezone, latitude, longitude, isp, organization, postal
        );
    }

    /**
     * Returns a formatted string for display purposes
     */
    public String getDisplayString() {
        StringBuilder sb = new StringBuilder();
        sb.append("IP Address: ").append(ip != null ? ip : "Unknown").append("\n");
        sb.append("City: ").append(city != null ? city : "Unknown").append("\n");
        sb.append("Region: ").append(region != null ? region : "Unknown").append("\n");
        sb.append("Country: ").append(country != null ? country : "Unknown").append("\n");
        
        if (countryCode != null) {
            sb.append("Country Code: ").append(countryCode).append("\n");
        }
        if (timezone != null) {
            sb.append("Timezone: ").append(timezone).append("\n");
        }
        if (latitude != null && longitude != null) {
            sb.append("Coordinates: ").append(latitude).append(", ").append(longitude).append("\n");
        }
        if (isp != null) {
            sb.append("ISP: ").append(isp).append("\n");
        }
        if (organization != null) {
            sb.append("Organization: ").append(organization).append("\n");
        }
        if (postal != null) {
            sb.append("Postal Code: ").append(postal).append("\n");
        }
        
        return sb.toString();
    }
}
