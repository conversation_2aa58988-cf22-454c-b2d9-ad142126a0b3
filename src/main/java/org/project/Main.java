package org.project;

import java.util.Scanner;

/**
 * Main launcher class for the IP Geolocation application
 * Provides options to run either GUI or console version
 */
public class Main {
    public static void main(String[] args) {
        System.out.println("=================================");
        System.out.println("   IP Geolocation Finder");
        System.out.println("=================================");
        System.out.println();

        // Check if GUI mode is requested via command line argument
        if (args.length > 0 && "gui".equalsIgnoreCase(args[0])) {
            System.out.println("Starting GUI version...");
            LocationApp.main(args);
            return;
        }

        // Check if console mode is explicitly requested
        if (args.length > 0 && "console".equalsIgnoreCase(args[0])) {
            System.out.println("Starting console version...");
            ConsoleLocationApp.main(args);
            return;
        }

        // If no arguments provided, ask user for preference
        Scanner scanner = new Scanner(System.in);
        System.out.println("Choose application mode:");
        System.out.println("1. GUI (JavaFX) - Graphical user interface");
        System.out.println("2. Console - Command line interface");
        System.out.print("Enter your choice (1 or 2): ");

        try {
            String choice = scanner.nextLine().trim();

            switch (choice) {
                case "1":
                case "gui":
                    System.out.println("Starting GUI version...");
                    LocationApp.main(args);
                    break;
                case "2":
                case "console":
                    System.out.println("Starting console version...");
                    ConsoleLocationApp.main(args);
                    break;
                default:
                    System.out.println("Invalid choice. Starting console version by default...");
                    ConsoleLocationApp.main(args);
                    break;
            }
        } catch (Exception e) {
            System.err.println("Error starting application: " + e.getMessage());
            System.out.println("Falling back to console version...");
            ConsoleLocationApp.main(args);
        } finally {
            scanner.close();
        }
    }
}