package org.project;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;

/**
 * Service class to fetch location data from IP geolocation APIs
 */
public class LocationService {
    
    private static final String IPINFO_API_URL = "http://ipinfo.io/json";
    private static final String IPAPI_API_URL = "http://ip-api.com/json/";
    private static final String IPIFY_API_URL = "https://api.ipify.org?format=json";
    
    private final HttpClient httpClient;
    private final Gson gson;
    
    public LocationService() {
        this.httpClient = HttpClient.newBuilder()
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        this.gson = new Gson();
    }
    
    /**
     * Get the public IP address of the user
     */
    public String getPublicIpAddress() throws IOException, InterruptedException {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(IPIFY_API_URL))
                .timeout(Duration.ofSeconds(10))
                .GET()
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() == 200) {
            JsonObject jsonObject = JsonParser.parseString(response.body()).getAsJsonObject();
            return jsonObject.get("ip").getAsString();
        } else {
            throw new IOException("Failed to get IP address. Status code: " + response.statusCode());
        }
    }
    
    /**
     * Fetch location data using ipinfo.io API
     */
    public LocationData getLocationFromIpInfo() throws IOException, InterruptedException {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(IPINFO_API_URL))
                .timeout(Duration.ofSeconds(15))
                .GET()
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() == 200) {
            return parseIpInfoResponse(response.body());
        } else {
            throw new IOException("Failed to get location data from ipinfo.io. Status code: " + response.statusCode());
        }
    }
    
    /**
     * Fetch location data using ip-api.com API
     */
    public LocationData getLocationFromIpApi() throws IOException, InterruptedException {
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(IPAPI_API_URL))
                .timeout(Duration.ofSeconds(15))
                .GET()
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() == 200) {
            return parseIpApiResponse(response.body());
        } else {
            throw new IOException("Failed to get location data from ip-api.com. Status code: " + response.statusCode());
        }
    }
    
    /**
     * Fetch location data for a specific IP address using ip-api.com
     */
    public LocationData getLocationForIp(String ipAddress) throws IOException, InterruptedException {
        String url = IPAPI_API_URL + ipAddress;
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .timeout(Duration.ofSeconds(15))
                .GET()
                .build();
        
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        
        if (response.statusCode() == 200) {
            return parseIpApiResponse(response.body());
        } else {
            throw new IOException("Failed to get location data for IP: " + ipAddress + ". Status code: " + response.statusCode());
        }
    }
    
    /**
     * Parse response from ipinfo.io API
     */
    private LocationData parseIpInfoResponse(String jsonResponse) {
        JsonObject jsonObject = JsonParser.parseString(jsonResponse).getAsJsonObject();
        LocationData locationData = new LocationData();
        
        locationData.setIp(getStringValue(jsonObject, "ip"));
        locationData.setCity(getStringValue(jsonObject, "city"));
        locationData.setRegion(getStringValue(jsonObject, "region"));
        locationData.setCountry(getStringValue(jsonObject, "country"));
        locationData.setTimezone(getStringValue(jsonObject, "timezone"));
        locationData.setOrganization(getStringValue(jsonObject, "org"));
        locationData.setPostal(getStringValue(jsonObject, "postal"));
        
        // Parse location coordinates if available
        String loc = getStringValue(jsonObject, "loc");
        if (loc != null && loc.contains(",")) {
            String[] coords = loc.split(",");
            if (coords.length == 2) {
                locationData.setLatitude(coords[0].trim());
                locationData.setLongitude(coords[1].trim());
            }
        }
        
        return locationData;
    }
    
    /**
     * Parse response from ip-api.com API
     */
    private LocationData parseIpApiResponse(String jsonResponse) {
        JsonObject jsonObject = JsonParser.parseString(jsonResponse).getAsJsonObject();
        LocationData locationData = new LocationData();
        
        // Check if the request was successful
        String status = getStringValue(jsonObject, "status");
        if (!"success".equals(status)) {
            throw new RuntimeException("API request failed: " + getStringValue(jsonObject, "message"));
        }
        
        locationData.setIp(getStringValue(jsonObject, "query"));
        locationData.setCity(getStringValue(jsonObject, "city"));
        locationData.setRegion(getStringValue(jsonObject, "regionName"));
        locationData.setCountry(getStringValue(jsonObject, "country"));
        locationData.setCountryCode(getStringValue(jsonObject, "countryCode"));
        locationData.setTimezone(getStringValue(jsonObject, "timezone"));
        locationData.setIsp(getStringValue(jsonObject, "isp"));
        locationData.setOrganization(getStringValue(jsonObject, "org"));
        locationData.setPostal(getStringValue(jsonObject, "zip"));
        
        // Parse coordinates
        if (jsonObject.has("lat")) {
            locationData.setLatitude(String.valueOf(jsonObject.get("lat").getAsDouble()));
        }
        if (jsonObject.has("lon")) {
            locationData.setLongitude(String.valueOf(jsonObject.get("lon").getAsDouble()));
        }
        
        return locationData;
    }
    
    /**
     * Safely extract string value from JSON object
     */
    private String getStringValue(JsonObject jsonObject, String key) {
        if (jsonObject.has(key) && !jsonObject.get(key).isJsonNull()) {
            return jsonObject.get(key).getAsString();
        }
        return null;
    }
    
    /**
     * Get location data with fallback to multiple APIs
     */
    public LocationData getLocationWithFallback() throws IOException, InterruptedException {
        try {
            // Try ip-api.com first (more detailed information)
            return getLocationFromIpApi();
        } catch (Exception e) {
            System.err.println("Failed to get location from ip-api.com: " + e.getMessage());
            try {
                // Fallback to ipinfo.io
                return getLocationFromIpInfo();
            } catch (Exception e2) {
                System.err.println("Failed to get location from ipinfo.io: " + e2.getMessage());
                throw new IOException("All location APIs failed", e2);
            }
        }
    }
    
    /**
     * Close the HTTP client
     */
    public void close() {
        // HttpClient doesn't need explicit closing in Java 11+
        // but this method is provided for consistency
    }
}
