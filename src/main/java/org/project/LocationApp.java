package org.project;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.stage.Stage;

/**
 * JavaFX GUI application for IP-based geolocation
 */
public class LocationApp extends Application {
    
    private LocationService locationService;
    private TextArea resultArea;
    private Button fetchButton;
    private Button fetchSpecificButton;
    private TextField ipTextField;
    private ProgressIndicator progressIndicator;
    private Label statusLabel;
    
    @Override
    public void start(Stage primaryStage) {
        locationService = new LocationService();
        
        primaryStage.setTitle("IP Geolocation Finder");
        primaryStage.setResizable(true);
        
        // Create the main layout
        VBox mainLayout = createMainLayout();
        
        Scene scene = new Scene(mainLayout, 600, 500);
        primaryStage.setScene(scene);
        primaryStage.show();
        
        // Handle application close
        primaryStage.setOnCloseRequest(event -> {
            locationService.close();
            Platform.exit();
        });
    }
    
    private VBox createMainLayout() {
        VBox mainLayout = new VBox(15);
        mainLayout.setPadding(new Insets(20));
        mainLayout.setAlignment(Pos.TOP_CENTER);
        
        // Title
        Label titleLabel = new Label("IP Geolocation Finder");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");
        
        // Current location section
        VBox currentLocationSection = createCurrentLocationSection();
        
        // Specific IP section
        VBox specificIpSection = createSpecificIpSection();
        
        // Results section
        VBox resultsSection = createResultsSection();
        
        // Status section
        HBox statusSection = createStatusSection();
        
        mainLayout.getChildren().addAll(
            titleLabel,
            new Separator(),
            currentLocationSection,
            new Separator(),
            specificIpSection,
            new Separator(),
            resultsSection,
            statusSection
        );
        
        return mainLayout;
    }
    
    private VBox createCurrentLocationSection() {
        VBox section = new VBox(10);
        section.setAlignment(Pos.CENTER);
        
        Label sectionLabel = new Label("Your Current Location");
        sectionLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");
        
        fetchButton = new Button("Get My Location");
        fetchButton.setStyle("-fx-font-size: 14px; -fx-padding: 10px 20px;");
        fetchButton.setOnAction(e -> fetchCurrentLocation());
        
        section.getChildren().addAll(sectionLabel, fetchButton);
        return section;
    }
    
    private VBox createSpecificIpSection() {
        VBox section = new VBox(10);
        section.setAlignment(Pos.CENTER);
        
        Label sectionLabel = new Label("Lookup Specific IP Address");
        sectionLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");
        
        HBox inputBox = new HBox(10);
        inputBox.setAlignment(Pos.CENTER);
        
        ipTextField = new TextField();
        ipTextField.setPromptText("Enter IP address (e.g., *******)");
        ipTextField.setPrefWidth(200);
        
        fetchSpecificButton = new Button("Lookup IP");
        fetchSpecificButton.setStyle("-fx-font-size: 14px; -fx-padding: 10px 20px;");
        fetchSpecificButton.setOnAction(e -> fetchSpecificIpLocation());
        
        inputBox.getChildren().addAll(ipTextField, fetchSpecificButton);
        section.getChildren().addAll(sectionLabel, inputBox);
        
        return section;
    }
    
    private VBox createResultsSection() {
        VBox section = new VBox(10);
        
        Label resultsLabel = new Label("Location Information");
        resultsLabel.setStyle("-fx-font-size: 16px; -fx-font-weight: bold;");
        
        resultArea = new TextArea();
        resultArea.setEditable(false);
        resultArea.setPrefRowCount(12);
        resultArea.setWrapText(true);
        resultArea.setStyle("-fx-font-family: monospace;");
        resultArea.setText("Click 'Get My Location' to see your location information...");
        
        section.getChildren().addAll(resultsLabel, resultArea);
        return section;
    }
    
    private HBox createStatusSection() {
        HBox statusSection = new HBox(10);
        statusSection.setAlignment(Pos.CENTER);
        
        progressIndicator = new ProgressIndicator();
        progressIndicator.setVisible(false);
        progressIndicator.setPrefSize(20, 20);
        
        statusLabel = new Label("Ready");
        statusLabel.setStyle("-fx-font-size: 12px;");
        
        statusSection.getChildren().addAll(progressIndicator, statusLabel);
        return statusSection;
    }
    
    private void fetchCurrentLocation() {
        setUIBusy(true, "Fetching your location...");
        
        Task<LocationData> task = new Task<LocationData>() {
            @Override
            protected LocationData call() throws Exception {
                return locationService.getLocationWithFallback();
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    LocationData location = getValue();
                    displayLocationData(location);
                    setUIBusy(false, "Location retrieved successfully");
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    displayError("Failed to fetch location: " + exception.getMessage());
                    setUIBusy(false, "Failed to retrieve location");
                });
            }
        };
        
        new Thread(task).start();
    }
    
    private void fetchSpecificIpLocation() {
        String ipAddress = ipTextField.getText().trim();
        if (ipAddress.isEmpty()) {
            showAlert("Please enter an IP address");
            return;
        }
        
        setUIBusy(true, "Fetching location for " + ipAddress + "...");
        
        Task<LocationData> task = new Task<LocationData>() {
            @Override
            protected LocationData call() throws Exception {
                return locationService.getLocationForIp(ipAddress);
            }
            
            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    LocationData location = getValue();
                    displayLocationData(location);
                    setUIBusy(false, "Location retrieved successfully");
                });
            }
            
            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    displayError("Failed to fetch location for " + ipAddress + ": " + exception.getMessage());
                    setUIBusy(false, "Failed to retrieve location");
                });
            }
        };
        
        new Thread(task).start();
    }
    
    private void displayLocationData(LocationData locationData) {
        if (locationData != null) {
            resultArea.setText(locationData.getDisplayString());
        } else {
            resultArea.setText("No location data available.");
        }
    }
    
    private void displayError(String errorMessage) {
        resultArea.setText("Error: " + errorMessage);
    }
    
    private void setUIBusy(boolean busy, String statusMessage) {
        fetchButton.setDisable(busy);
        fetchSpecificButton.setDisable(busy);
        progressIndicator.setVisible(busy);
        statusLabel.setText(statusMessage);
    }
    
    private void showAlert(String message) {
        Alert alert = new Alert(Alert.AlertType.WARNING);
        alert.setTitle("Input Error");
        alert.setHeaderText(null);
        alert.setContentText(message);
        alert.showAndWait();
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
